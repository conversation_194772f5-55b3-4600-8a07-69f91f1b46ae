<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ميزة تحويل حقل الولاية إلى نص</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار ميزة تحويل حقل الولاية إلى نص</h1>
        
        <div class="form-group">
            <label>حالة الميزة الحالية:</label>
            <div id="current-status" class="status info">
                جاري التحقق...
            </div>
        </div>

        <div class="form-group">
            <label for="test-state">حقل الولاية (للاختبار):</label>
            <select id="test-state" name="state">
                <option value="" disabled selected>اختر الولاية</option>
                <option value="01">01 - الجزائر</option>
                <option value="02">02 - الشلف</option>
                <option value="03">03 - الأغواط</option>
                <option value="04">04 - أم البواقي</option>
                <option value="05">05 - باتنة</option>
            </select>
        </div>

        <div class="form-group">
            <button onclick="convertToText()">تحويل إلى حقل نص</button>
            <button onclick="convertToSelect()">تحويل إلى قائمة اختيار</button>
        </div>

        <div class="form-group">
            <label>نوع الحقل الحالي:</label>
            <div id="field-type" class="status info">
                قائمة اختيار (select)
            </div>
        </div>

        <div class="form-group">
            <label>القيمة الحالية:</label>
            <div id="current-value" class="status info">
                لا توجد قيمة
            </div>
        </div>
    </div>

    <script>
        // محاكاة بيانات rid_cod_params
        var rid_cod_params = {
            select_state: 'اختر الولاية',
            show_states: true,
            state_as_text: false
        };

        // محاكاة بيانات الولايات
        var statesData = {
            '01': 'الجزائر',
            '02': 'الشلف', 
            '03': 'الأغواط',
            '04': 'أم البواقي',
            '05': 'باتنة'
        };

        var stateField = $('#test-state');

        function updateStatus() {
            var fieldType = stateField.is('select') ? 'قائمة اختيار (select)' : 'حقل نص (input)';
            var currentValue = stateField.val() || 'لا توجد قيمة';
            
            $('#field-type').text(fieldType);
            $('#current-value').text(currentValue);
            
            var statusText = rid_cod_params.state_as_text ? 
                'مفعلة - حقل الولاية كنص' : 
                'غير مفعلة - حقل الولاية كقائمة اختيار';
            $('#current-status').text(statusText);
        }

        function convertToText() {
            var currentValue = stateField.val() || '';
            var container = stateField.parent();
            var placeholder = rid_cod_params.select_state || 'اختر الولاية';
            
            // إزالة الحقل الحالي
            stateField.remove();
            
            // إنشاء حقل نص جديد
            var textInput = $('<input>', {
                type: 'text',
                id: 'test-state',
                name: 'state',
                placeholder: placeholder,
                value: currentValue
            });
            
            // إضافة الحقل الجديد
            container.append(textInput);
            
            // تحديث المرجع
            stateField = textInput;
            rid_cod_params.state_as_text = true;
            
            // تحديث الحالة
            updateStatus();
            
            console.log('تم التحويل إلى حقل نص');
        }

        function convertToSelect() {
            var currentValue = stateField.val() || '';
            var container = stateField.parent();
            
            // إزالة الحقل الحالي
            stateField.remove();
            
            // إنشاء قائمة اختيار جديدة
            var selectElement = $('<select>', {
                id: 'test-state',
                name: 'state'
            });
            
            // إضافة الخيار الافتراضي
            selectElement.append('<option value="" disabled selected>' + rid_cod_params.select_state + '</option>');
            
            // إضافة الولايات
            $.each(statesData, function(stateCode, stateName) {
                var displayText = stateCode + ' - ' + stateName;
                selectElement.append('<option value="' + stateCode + '">' + displayText + '</option>');
            });
            
            // إضافة الحقل الجديد
            container.append(selectElement);
            
            // تحديث المرجع
            stateField = selectElement;
            rid_cod_params.state_as_text = false;
            
            // تحديث الحالة
            updateStatus();
            
            console.log('تم التحويل إلى قائمة اختيار');
        }

        // تحديث الحالة عند تغيير القيمة
        $(document).on('change input', '#test-state', function() {
            updateStatus();
        });

        // تحديث الحالة الأولية
        $(document).ready(function() {
            updateStatus();
        });
    </script>
</body>
</html>
