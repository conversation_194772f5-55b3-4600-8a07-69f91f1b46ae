<?php
/**
 * ملف للتحقق من إعدادات الإضافة
 * يجب تشغيله في بيئة WordPress
 */

// التحقق من أن WordPress محمل
if (!defined('ABSPATH')) {
    die('هذا الملف يجب تشغيله في بيئة WordPress');
}

echo "<h2>فحص إعدادات RID COD</h2>";

// التحقق من الإعدادات الأساسية
$show_states = get_option('rid_cod_show_states', 'yes');
$state_as_text = get_option('rid_cod_state_as_text', 'no');
$show_cities = get_option('rid_cod_show_cities', 'yes');

echo "<h3>الإعدادات الحالية:</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>الإعداد</th><th>القيمة الخام</th><th>القيمة المنطقية</th></tr>";
echo "<tr><td>rid_cod_show_states</td><td>$show_states</td><td>" . ($show_states === 'yes' ? 'true' : 'false') . "</td></tr>";
echo "<tr><td>rid_cod_state_as_text</td><td>$state_as_text</td><td>" . ($state_as_text === 'yes' ? 'true' : 'false') . "</td></tr>";
echo "<tr><td>rid_cod_show_cities</td><td>$show_cities</td><td>" . ($show_cities === 'yes' ? 'true' : 'false') . "</td></tr>";
echo "</table>";

// التحقق من بيانات الدولة
if (class_exists('RID_COD_Country_Manager')) {
    $current_country = RID_COD_Country_Manager::get_current_country();
    $states = RID_COD_Country_Manager::get_states_by_country($current_country);
    
    echo "<h3>بيانات الدولة:</h3>";
    echo "<p><strong>الدولة الحالية:</strong> $current_country</p>";
    echo "<p><strong>عدد الولايات:</strong> " . count($states) . "</p>";
    
    if (!empty($states)) {
        echo "<p><strong>أول 5 ولايات:</strong></p>";
        echo "<ul>";
        $count = 0;
        foreach ($states as $code => $name) {
            if ($count >= 5) break;
            echo "<li>$code - $name</li>";
            $count++;
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'><strong>تحذير:</strong> لا توجد ولايات للدولة الحالية!</p>";
    }
} else {
    echo "<p style='color: red;'><strong>خطأ:</strong> فئة RID_COD_Country_Manager غير موجودة!</p>";
}

// محاكاة منطق النموذج
echo "<h3>محاكاة منطق النموذج:</h3>";

$show_states_bool = ($show_states === 'yes');
$state_as_text_bool = ($state_as_text === 'yes');

echo "<div style='background-color: #f0f8ff; padding: 15px; border: 1px solid #007cba; border-radius: 5px;'>";

if ($show_states_bool) {
    echo "<p>✅ show_states = true</p>";
    
    if ($state_as_text_bool) {
        echo "<p>✅ state_as_text = true</p>";
        echo "<p><strong>النتيجة:</strong> سيتم عرض حقل نص للولاية</p>";
        echo "<input type='text' placeholder='الولاية' style='padding: 5px; width: 200px;'>";
        
    } elseif (!empty($states)) {
        echo "<p>✅ state_as_text = false و الولايات متاحة</p>";
        echo "<p><strong>النتيجة:</strong> سيتم عرض قائمة اختيار للولايات</p>";
        echo "<select style='padding: 5px; width: 200px;'>";
        echo "<option value='' disabled selected>اختر الولاية</option>";
        $count = 0;
        foreach ($states as $code => $name) {
            if ($count >= 5) break;
            echo "<option value='$code'>$code - $name</option>";
            $count++;
        }
        echo "</select>";
        
    } else {
        echo "<p>⚠️ state_as_text = false لكن لا توجد ولايات</p>";
        echo "<p><strong>النتيجة:</strong> سيتم عرض حقل نص للولاية (احتياطي)</p>";
        echo "<input type='text' placeholder='الولاية' style='padding: 5px; width: 200px;'>";
    }
    
} else {
    echo "<p>❌ show_states = false</p>";
    echo "<p><strong>النتيجة:</strong> لن يتم عرض حقل الولاية</p>";
}

echo "</div>";

// اختبار تحديث الإعدادات
echo "<h3>اختبار تحديث الإعدادات:</h3>";

if (isset($_POST['update_settings'])) {
    $new_state_as_text = isset($_POST['state_as_text']) ? 'yes' : 'no';
    update_option('rid_cod_state_as_text', $new_state_as_text);
    echo "<p style='color: green;'>تم تحديث الإعداد إلى: $new_state_as_text</p>";
    echo "<script>window.location.reload();</script>";
}

echo "<form method='post'>";
echo "<label>";
echo "<input type='checkbox' name='state_as_text' " . ($state_as_text_bool ? 'checked' : '') . "> تفعيل تحويل حقل الولاية إلى نص";
echo "</label><br><br>";
echo "<input type='submit' name='update_settings' value='تحديث الإعداد' style='background-color: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>";
echo "</form>";

// معلومات إضافية للتشخيص
echo "<h3>معلومات إضافية:</h3>";
echo "<ul>";
echo "<li><strong>إصدار WordPress:</strong> " . get_bloginfo('version') . "</li>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>الوقت الحالي:</strong> " . current_time('mysql') . "</li>";
echo "<li><strong>المنطقة الزمنية:</strong> " . get_option('timezone_string') . "</li>";
echo "</ul>";

// التحقق من وجود الملفات المطلوبة
echo "<h3>التحقق من الملفات:</h3>";
$files_to_check = [
    'includes/class-rid-cod-form.php',
    'includes/class-rid-cod-customizer.php',
    'assets/js/rid-cod.js'
];

foreach ($files_to_check as $file) {
    $full_path = plugin_dir_path(__FILE__) . $file;
    if (file_exists($full_path)) {
        echo "<p>✅ $file موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $file غير موجود</p>";
    }
}
?>
