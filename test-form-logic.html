<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار منطق النموذج</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .result {
            background-color: #f0f8ff;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .success { border-left-color: #28a745; background-color: #d4edda; }
        .warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .error { border-left-color: #dc3545; background-color: #f8d7da; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .controls {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
    </style>
</head>
<body>
    <h1>اختبار منطق عرض حقل الولاية</h1>
    
    <div class="controls">
        <h3>التحكم في الإعدادات:</h3>
        <label>
            <input type="checkbox" id="show_states" checked> إظهار حقل الولايات (show_states)
        </label><br>
        <label>
            <input type="checkbox" id="state_as_text"> تحويل حقل الولاية إلى نص (state_as_text)
        </label><br>
        <label>
            <input type="checkbox" id="has_states" checked> توجد ولايات متاحة للدولة (!empty($states))
        </label><br>
        <button onclick="updateTest()">تحديث الاختبار</button>
    </div>

    <div id="test-result" class="test-case">
        <h3>النتيجة:</h3>
        <div id="result-content"></div>
    </div>

    <div class="test-case">
        <h3>الكود المطبق:</h3>
        <pre id="applied-code" style="background-color: #f8f9fa; padding: 10px; border-radius: 4px;"></pre>
    </div>

    <script>
        function updateTest() {
            const showStates = document.getElementById('show_states').checked;
            const stateAsText = document.getElementById('state_as_text').checked;
            const hasStates = document.getElementById('has_states').checked;
            
            const resultContent = document.getElementById('result-content');
            const appliedCode = document.getElementById('applied-code');
            
            let result = '';
            let code = '';
            
            // تطبيق نفس المنطق الموجود في PHP
            if (showStates) {
                code += 'if ($show_states) {\n';
                
                if (stateAsText) {
                    code += '    if ($state_as_text) {\n';
                    code += '        // عرض حقل الولاية كحقل نص\n';
                    code += '        <input type="text" id="rid-cod-state-text" name="state" placeholder="الولاية" required>\n';
                    code += '    }\n';
                    
                    result = '<div class="result success">';
                    result += '<strong>✅ سيتم عرض حقل نص للولاية</strong><br>';
                    result += '<input type="text" placeholder="الولاية" style="width: 200px;">';
                    result += '</div>';
                    
                } else if (hasStates) {
                    code += '    } elseif (!empty($states)) {\n';
                    code += '        // عرض حقل الولاية كقائمة اختيار\n';
                    code += '        <select id="rid-cod-state" name="state" required>\n';
                    code += '            <option value="" disabled selected>اختر الولاية</option>\n';
                    code += '            // ... خيارات الولايات\n';
                    code += '        </select>\n';
                    code += '    }\n';
                    
                    result = '<div class="result success">';
                    result += '<strong>✅ سيتم عرض قائمة اختيار للولايات</strong><br>';
                    result += '<select style="width: 200px;"><option>اختر الولاية</option><option>01 - الجزائر</option><option>02 - الشلف</option></select>';
                    result += '</div>';
                    
                } else {
                    code += '    } else {\n';
                    code += '        // عرض حقل الولاية كحقل نص (عندما لا توجد ولايات متاحة)\n';
                    code += '        <input type="text" id="rid-cod-state-text" name="state" placeholder="الولاية" required>\n';
                    code += '    }\n';
                    
                    result = '<div class="result warning">';
                    result += '<strong>⚠️ سيتم عرض حقل نص للولاية (لا توجد ولايات متاحة)</strong><br>';
                    result += '<input type="text" placeholder="الولاية" style="width: 200px;">';
                    result += '</div>';
                }
                
                code += '}\n';
                
            } else {
                code += 'if (!$show_states) {\n';
                code += '    // لن يتم عرض حقل الولاية\n';
                code += '}\n';
                
                result = '<div class="result error">';
                result += '<strong>❌ لن يتم عرض حقل الولاية</strong><br>';
                result += '<em>حقل الولايات مخفي في الإعدادات</em>';
                result += '</div>';
            }
            
            resultContent.innerHTML = result;
            appliedCode.textContent = code;
        }
        
        // تحديث الاختبار عند تحميل الصفحة
        updateTest();
        
        // تحديث الاختبار عند تغيير أي إعداد
        document.getElementById('show_states').addEventListener('change', updateTest);
        document.getElementById('state_as_text').addEventListener('change', updateTest);
        document.getElementById('has_states').addEventListener('change', updateTest);
    </script>

    <div class="test-case">
        <h3>حالات الاختبار الشائعة:</h3>
        
        <h4>1. الحالة الافتراضية (يجب أن تعمل):</h4>
        <ul>
            <li>✅ إظهار حقل الولايات: مفعل</li>
            <li>❌ تحويل إلى نص: غير مفعل</li>
            <li>✅ توجد ولايات: نعم</li>
            <li><strong>النتيجة المتوقعة:</strong> قائمة اختيار للولايات</li>
        </ul>
        
        <h4>2. تفعيل تحويل إلى نص:</h4>
        <ul>
            <li>✅ إظهار حقل الولايات: مفعل</li>
            <li>✅ تحويل إلى نص: مفعل</li>
            <li>✅ توجد ولايات: نعم</li>
            <li><strong>النتيجة المتوقعة:</strong> حقل نص للولاية</li>
        </ul>
        
        <h4>3. إخفاء حقل الولايات:</h4>
        <ul>
            <li>❌ إظهار حقل الولايات: غير مفعل</li>
            <li>❌ تحويل إلى نص: غير مفعل</li>
            <li>✅ توجد ولايات: نعم</li>
            <li><strong>النتيجة المتوقعة:</strong> لا يوجد حقل ولاية</li>
        </ul>
    </div>

    <div class="test-case">
        <h3>استكشاف الأخطاء:</h3>
        <p>إذا كان حقل الولاية لا يظهر في موقعك، تحقق من:</p>
        <ol>
            <li><strong>إعدادات الإضافة:</strong> تأكد من أن "إظهار حقل الولايات" مفعل</li>
            <li><strong>بيانات الدولة:</strong> تأكد من وجود ولايات للدولة المحددة</li>
            <li><strong>أخطاء PHP:</strong> تحقق من سجل أخطاء الخادم</li>
            <li><strong>أخطاء JavaScript:</strong> افتح أدوات المطور في المتصفح</li>
            <li><strong>التخزين المؤقت:</strong> امسح التخزين المؤقت للموقع</li>
        </ol>
    </div>
</body>
</html>
