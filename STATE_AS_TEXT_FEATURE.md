# ميزة تحويل حقل الولاية إلى نص - RID COD Plugin

## نظرة عامة
تم إضافة ميزة جديدة تسمح بتحويل حقل الولاية من قائمة اختيار (dropdown) إلى حقل نص قابل للكتابة.

## الميزة الجديدة

### 1. إعداد جديد في تبويبة "إعدادات التحكم في النموذج"
- **اسم الإعداد**: "تحويل حقل الولاية إلى نص"
- **المعرف**: `rid_cod_state_as_text`
- **القيمة الافتراضية**: `no` (غير مفعل)
- **الوصف**: "عند التفعيل، سيتم تحويل حقل الولاية من قائمة اختيار إلى حقل نص قابل للكتابة."

### 2. كيفية عمل الميزة

#### عندما تكون الميزة غير مفعلة (الوضع الافتراضي):
- يتم عرض حقل الولاية كقائمة اختيار (dropdown) تحتوي على الولايات المتاحة للدولة المحددة
- يمكن للمستخدم اختيار الولاية من القائمة المنسدلة

#### عندما تكون الميزة مفعلة:
- يتم عرض حقل الولاية كحقل نص قابل للكتابة
- يمكن للمستخدم كتابة اسم الولاية يدوياً
- يتم الاحتفاظ بجميع الوظائف الأخرى مثل حفظ المسودة وحساب أسعار التوصيل

## التغييرات التقنية

### 1. ملف الإعدادات (`includes/class-rid-cod-customizer.php`)
```php
// إضافة الإعداد الجديد
add_settings_field('rid_cod_state_as_text', __('تحويل حقل الولاية إلى نص', 'rid-cod'), 
    array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_form_control', 
    ['id' => 'rid_cod_state_as_text', 'default' => 'no', 
     'desc' => __('عند التفعيل، سيتم تحويل حقل الولاية من قائمة اختيار إلى حقل نص قابل للكتابة.', 'rid-cod')]);

// تسجيل الإعداد
register_setting('rid_cod_settings', 'rid_cod_state_as_text', 
    ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
```

### 2. ملف النموذج (`includes/class-rid-cod-form.php`)
```php
// الحصول على الإعداد الجديد
$state_as_text = get_option('rid_cod_state_as_text', 'no') === 'yes';

// تعديل منطق عرض حقل الولاية
<?php if (!empty($states) && $show_states && !$state_as_text) : ?>
    <!-- عرض حقل الولاية كقائمة اختيار -->
<?php elseif ($show_states && $state_as_text) : ?>
    <!-- عرض حقل الولاية كحقل نص -->
<?php endif; ?>

// تمرير الإعداد إلى JavaScript
'state_as_text' => get_option('rid_cod_state_as_text', 'no') === 'yes',
```

### 3. ملف JavaScript (`assets/js/rid-cod.js`)
```javascript
// وظائف التحويل بين select و text input
function convertStateSelectToTextInput() {
    // تحويل حقل الولاية من select إلى text input
}

function convertStateTextToSelectInput() {
    // تحويل حقل الولاية من text input إلى select
}

// تطبيق الإعداد عند تحميل الصفحة
if (rid_cod_params.state_as_text) {
    if ($('#rid-cod-state').length > 0) {
        convertStateSelectToTextInput();
    }
}
```

## حالات الاستخدام

### 1. للمتاجر التي تخدم مناطق غير مدرجة في قوائم الولايات الافتراضية
- يمكن للعملاء كتابة أسماء المناطق أو الولايات الخاصة بهم

### 2. للمتاجر الدولية
- يمكن للعملاء من دول مختلفة كتابة أسماء ولاياتهم بحرية

### 3. لتبسيط عملية الطلب
- تقليل الخطوات المطلوبة من العميل عندما لا تكون قائمة الولايات ضرورية

## الميزات المحافظ عليها

### 1. حفظ المسودة
- يتم حفظ النص المكتوب في حقل الولاية تلقائياً

### 2. التحقق من صحة البيانات
- يبقى حقل الولاية مطلوباً إذا كان مفعلاً في الإعدادات

### 3. التوافق مع الميزات الأخرى
- تعمل الميزة بسلاسة مع جميع الميزات الأخرى في الإضافة

## ملاحظات مهمة

### 1. الأولوية
- إذا كانت الميزة مفعلة، فإنها تتجاوز العرض الافتراضي للولايات
- حتى لو كانت هناك ولايات متاحة للدولة، سيتم عرض حقل نص

### 2. التوافق مع إخفاء الولايات
- إذا كان إعداد "إظهار حقل الولايات" غير مفعل، فإن هذه الميزة لن تؤثر

### 3. تحديث المراجع
- يتم تحديث المراجع في JavaScript تلقائياً عند التحويل بين الأنواع

## اختبار الميزة

### 1. تفعيل الميزة
1. اذهب إلى إعدادات الإضافة
2. انتقل إلى تبويبة "إعدادات التحكم في النموذج"
3. فعل خيار "تحويل حقل الولاية إلى نص"
4. احفظ الإعدادات

### 2. التحقق من النتيجة
1. اذهب إلى صفحة منتج
2. تحقق من أن حقل الولاية أصبح حقل نص قابل للكتابة
3. اكتب نص في الحقل وتأكد من أن النموذج يعمل بشكل صحيح

## الخلاصة
هذه الميزة توفر مرونة إضافية للمتاجر التي تحتاج إلى حرية أكبر في إدخال معلومات الولاية، مع الحفاظ على جميع الوظائف الأساسية للإضافة.
