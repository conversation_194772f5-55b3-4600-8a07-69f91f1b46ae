<?php
/**
 * ملف اختبار لتشخيص مشكلة حقل الولاية
 */

// محاكاة إعدادات WordPress
$show_states = true; // محاكاة get_option('rid_cod_show_states', 'yes') === 'yes'
$state_as_text = false; // محاكاة get_option('rid_cod_state_as_text', 'no') === 'yes'

// محاكاة بيانات الولايات
$states = array(
    '01' => 'الجزائر',
    '02' => 'الشلف',
    '03' => 'الأغواط',
    '04' => 'أم البواقي',
    '05' => 'باتنة'
);

echo "<h2>تشخيص حقل الولاية</h2>";

echo "<h3>الإعدادات الحالية:</h3>";
echo "<ul>";
echo "<li>show_states: " . ($show_states ? 'true' : 'false') . "</li>";
echo "<li>state_as_text: " . ($state_as_text ? 'true' : 'false') . "</li>";
echo "<li>عدد الولايات المتاحة: " . count($states) . "</li>";
echo "<li>الولايات فارغة؟ " . (empty($states) ? 'نعم' : 'لا') . "</li>";
echo "</ul>";

echo "<h3>المنطق المطبق:</h3>";

if ($show_states) {
    echo "<p>✅ show_states = true، سيتم عرض حقل الولاية</p>";
    
    if ($state_as_text) {
        echo "<p>✅ state_as_text = true، سيتم عرض حقل نص</p>";
        echo "<div style='border: 2px solid green; padding: 10px; margin: 10px 0;'>";
        echo "<strong>النتيجة: حقل نص</strong><br>";
        echo '<input type="text" placeholder="الولاية" style="width: 200px; padding: 5px;">';
        echo "</div>";
    } elseif (!empty($states)) {
        echo "<p>✅ state_as_text = false و الولايات متاحة، سيتم عرض قائمة اختيار</p>";
        echo "<div style='border: 2px solid blue; padding: 10px; margin: 10px 0;'>";
        echo "<strong>النتيجة: قائمة اختيار</strong><br>";
        echo '<select style="width: 200px; padding: 5px;">';
        echo '<option value="" disabled selected>اختر الولاية</option>';
        foreach ($states as $code => $name) {
            echo "<option value='$code'>$code - $name</option>";
        }
        echo '</select>';
        echo "</div>";
    } else {
        echo "<p>⚠️ state_as_text = false لكن لا توجد ولايات، سيتم عرض حقل نص</p>";
        echo "<div style='border: 2px solid orange; padding: 10px; margin: 10px 0;'>";
        echo "<strong>النتيجة: حقل نص (احتياطي)</strong><br>";
        echo '<input type="text" placeholder="الولاية" style="width: 200px; padding: 5px;">';
        echo "</div>";
    }
} else {
    echo "<p>❌ show_states = false، لن يتم عرض حقل الولاية</p>";
    echo "<div style='border: 2px solid red; padding: 10px; margin: 10px 0;'>";
    echo "<strong>النتيجة: لا يوجد حقل ولاية</strong>";
    echo "</div>";
}

echo "<hr>";

echo "<h3>اختبار الحالات المختلفة:</h3>";

// اختبار الحالة 1: show_states = true, state_as_text = false, states متاحة
echo "<h4>الحالة 1: إعدادات افتراضية مع ولايات متاحة</h4>";
$test_show_states = true;
$test_state_as_text = false;
$test_states = $states;

echo "show_states = true, state_as_text = false, states متاحة<br>";
if ($test_show_states) {
    if ($test_state_as_text) {
        echo "النتيجة: حقل نص<br>";
    } elseif (!empty($test_states)) {
        echo "✅ النتيجة: قائمة اختيار<br>";
    } else {
        echo "النتيجة: حقل نص (احتياطي)<br>";
    }
} else {
    echo "النتيجة: لا يوجد حقل<br>";
}

// اختبار الحالة 2: show_states = true, state_as_text = true
echo "<h4>الحالة 2: تفعيل تحويل إلى نص</h4>";
$test_show_states = true;
$test_state_as_text = true;
$test_states = $states;

echo "show_states = true, state_as_text = true<br>";
if ($test_show_states) {
    if ($test_state_as_text) {
        echo "✅ النتيجة: حقل نص<br>";
    } elseif (!empty($test_states)) {
        echo "النتيجة: قائمة اختيار<br>";
    } else {
        echo "النتيجة: حقل نص (احتياطي)<br>";
    }
} else {
    echo "النتيجة: لا يوجد حقل<br>";
}

// اختبار الحالة 3: show_states = false
echo "<h4>الحالة 3: إخفاء حقل الولايات</h4>";
$test_show_states = false;
$test_state_as_text = false;
$test_states = $states;

echo "show_states = false<br>";
if ($test_show_states) {
    if ($test_state_as_text) {
        echo "النتيجة: حقل نص<br>";
    } elseif (!empty($test_states)) {
        echo "النتيجة: قائمة اختيار<br>";
    } else {
        echo "النتيجة: حقل نص (احتياطي)<br>";
    }
} else {
    echo "✅ النتيجة: لا يوجد حقل<br>";
}

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كان حقل الولاية لا يظهر في موقعك، تحقق من:</p>";
echo "<ul>";
echo "<li>إعداد 'إظهار حقل الولايات' مفعل</li>";
echo "<li>وجود ولايات للدولة المحددة</li>";
echo "<li>عدم وجود أخطاء في JavaScript</li>";
echo "</ul>";
?>
